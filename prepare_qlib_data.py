#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os
import sys
import logging
import subprocess
from pathlib import Path
import shutil
import numpy as np
import qlib
from qlib.data import D
from qlib.config import REG_CN

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def prepare_data(input_file, output_file, symbol):
    """
    准备数据以便导入到qlib中

    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        symbol: 股票代码
    """
    logger.info(f"读取文件: {input_file}")
    df = pd.read_csv(input_file)

    # 添加symbol列
    df['symbol'] = symbol

    # 重命名列以匹配qlib格式
    column_mapping = {
        'datetime': 'date',
        'openPrice': 'open',
        'highPrice': 'high',
        'lowPrice': 'low',
        'closePrice': 'close',
        'tradeVolume': 'volume',
        'amount': 'amount',
        'averagePrice': 'averageprice',
        'IOPV': 'iopv'
    }

    df = df.rename(columns=column_mapping)

    # 确保日期格式正确
    df['date'] = pd.to_datetime(df['date'], format='%Y%m%d%H%M%S')

    # 按日期排序
    df = df.sort_values(by='date')

    logger.info(f"数据行数: {len(df)}")
    logger.info(f"数据日期范围: {df['date'].min()} 至 {df['date'].max()}")

    # 保存处理后的数据
    logger.info(f"保存处理后的数据到: {output_file}")
    df.to_csv(output_file, index=False)

    return df

def dump_to_qlib(csv_file, qlib_dir, symbol, freq="5min"):
    """
    将CSV数据导入到qlib中

    Args:
        csv_file: CSV文件路径
        qlib_dir: qlib数据目录
        symbol: 股票代码
        freq: 数据频率
    """
    logger.info(f"将数据导入到qlib: {csv_file} -> {qlib_dir}")

    features_dir = os.path.join(qlib_dir, "features")
    calendars_dir = os.path.join(qlib_dir, "calendars")
    instruments_dir = os.path.join(qlib_dir, "instruments")

    # 确保目录存在
    os.makedirs(features_dir, exist_ok=True)
    os.makedirs(calendars_dir, exist_ok=True)
    os.makedirs(instruments_dir, exist_ok=True)

    # 只清除当前股票的特征目录（如果存在）
    symbol_dir = os.path.join(features_dir, symbol)
    if os.path.exists(symbol_dir):
        logger.info(f"清除现有的股票特征目录: {symbol_dir}")
        shutil.rmtree(symbol_dir)

    # 使用dump_bin.py脚本导入数据
    cmd = [
        "python", "qlib/scripts/dump_bin.py", "dump_all",
        f"--csv_path={csv_file}",
        f"--qlib_dir={qlib_dir}",
        f"--freq={freq}",
        "--date_field_name=date",
        "--symbol_field_name=symbol",
        "--exclude_fields=symbol"
    ]

    logger.info(f"执行命令: {' '.join(cmd)}")

    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info("数据导入成功")
        logger.info(result.stdout)
    except subprocess.CalledProcessError as e:
        logger.error(f"数据导入失败: {e}")
        logger.error(e.stdout)
        logger.error(e.stderr)
        return False

    # 修复特征目录名称
    fix_feature_directory(qlib_dir, symbol)

    # 清理instruments文件
    clean_instruments_file(qlib_dir)

    # 更新股票代码
    update_instruments(qlib_dir, symbol)

    return True

def update_instruments(qlib_dir, symbol):
    """
    更新股票列表文件

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
    """
    logger.info(f"更新股票列表文件: {symbol}")

    instruments_file = os.path.join(qlib_dir, "instruments", "all.txt")

    # 从CSV文件中获取日期范围
    csv_file = f"prepared_{symbol}.csv"
    if os.path.exists(csv_file):
        df = pd.read_csv(csv_file)
        df['date'] = pd.to_datetime(df['date'])
        start_date = df['date'].min().strftime('%Y-%m-%d %H:%M:%S')
        end_date = df['date'].max().strftime('%Y-%m-%d %H:%M:%S')
    else:
        logger.warning(f"找不到CSV文件: {csv_file}，使用默认日期范围")
        start_date = "2000-01-01 00:00:00"
        end_date = "2099-12-31 23:59:59"

    # 准备新的股票条目
    new_entry = f"{symbol}\t{start_date}\t{end_date}\n"

    # 如果文件存在，读取并更新
    if os.path.exists(instruments_file):
        with open(instruments_file, 'r') as f:
            lines = f.readlines()

        # 检查是否已存在该股票，如果存在则更新，否则添加
        updated = False
        new_lines = []
        for line in lines:
            parts = line.strip().split('\t')
            if len(parts) >= 1 and parts[0] == symbol:
                new_lines.append(new_entry)
                updated = True
            else:
                new_lines.append(line)

        if not updated:
            new_lines.append(new_entry)
    else:
        # 如果文件不存在，创建新文件
        new_lines = [new_entry]

    # 写回instruments文件
    with open(instruments_file, 'w') as f:
        f.writelines(new_lines)

    logger.info(f"股票列表文件更新成功: {instruments_file}")

    # 确保所有特征目录对应的股票都在instruments文件中
    ensure_all_features_in_instruments(qlib_dir)

def ensure_all_features_in_instruments(qlib_dir):
    """
    确保所有特征目录对应的股票都在instruments文件中

    Args:
        qlib_dir: qlib数据目录
    """
    logger.info("确保所有特征目录对应的股票都在instruments文件中...")

    features_dir = os.path.join(qlib_dir, "features")
    instruments_file = os.path.join(qlib_dir, "instruments", "all.txt")

    # 获取所有特征目录（股票代码）
    feature_symbols = []
    for item in os.listdir(features_dir):
        item_path = os.path.join(features_dir, item)
        if os.path.isdir(item_path) and not item.startswith("prepared_"):
            feature_symbols.append(item)

    logger.info(f"发现特征目录: {feature_symbols}")

    # 读取instruments文件中的股票代码
    instrument_symbols = []
    if os.path.exists(instruments_file):
        with open(instruments_file, 'r') as f:
            for line in f:
                parts = line.strip().split('\t')
                if len(parts) >= 1:
                    instrument_symbols.append(parts[0])

    logger.info(f"instruments文件中的股票: {instrument_symbols}")

    # 找出缺失的股票
    missing_symbols = [s for s in feature_symbols if s not in instrument_symbols]

    # 为缺失的股票添加条目
    if missing_symbols:
        logger.info(f"添加缺失的股票到instruments文件: {missing_symbols}")

        with open(instruments_file, 'a') as f:
            for symbol in missing_symbols:
                # 从CSV文件中获取日期范围
                csv_file = f"prepared_{symbol}.csv"
                if os.path.exists(csv_file):
                    df = pd.read_csv(csv_file)
                    df['date'] = pd.to_datetime(df['date'])
                    start_date = df['date'].min().strftime('%Y-%m-%d %H:%M:%S')
                    end_date = df['date'].max().strftime('%Y-%m-%d %H:%M:%S')
                else:
                    logger.warning(f"找不到CSV文件: {csv_file}，使用默认日期范围")
                    start_date = "2000-01-01 00:00:00"
                    end_date = "2099-12-31 23:59:59"

                # 添加条目
                f.write(f"{symbol}\t{start_date}\t{end_date}\n")

        logger.info("缺失的股票已添加到instruments文件")
    else:
        logger.info("所有股票都已在instruments文件中")

def fix_feature_directory(qlib_dir, symbol):
    """
    修复特征目录名称

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
    """
    logger.info(f"修复特征目录名称: {symbol}")

    features_dir = os.path.join(qlib_dir, "features")

    # 查找所有特征目录
    for item in os.listdir(features_dir):
        item_path = os.path.join(features_dir, item)
        if os.path.isdir(item_path) and item.startswith("prepared_") and item != symbol:
            # 如果目录名以prepared_开头且不是symbol，则重命名
            target_path = os.path.join(features_dir, symbol)
            logger.info(f"重命名特征目录: {item_path} -> {target_path}")

            # 如果目标目录已存在，先删除
            if os.path.exists(target_path):
                shutil.rmtree(target_path)

            # 重命名目录
            shutil.move(item_path, target_path)

            logger.info(f"特征目录重命名成功: {target_path}")
            break

    # 检查特征目录是否存在
    symbol_dir = os.path.join(features_dir, symbol)
    if not os.path.exists(symbol_dir):
        logger.error(f"特征目录不存在: {symbol_dir}")
        return False

    return True

def clean_instruments_file(qlib_dir):
    """
    清理instruments文件，删除以PREPARED_开头的条目

    Args:
        qlib_dir: qlib数据目录
    """
    logger.info("清理instruments文件...")

    instruments_file = os.path.join(qlib_dir, "instruments", "all.txt")
    if not os.path.exists(instruments_file):
        logger.warning(f"instruments文件不存在: {instruments_file}")
        return

    # 读取instruments文件
    with open(instruments_file, 'r') as f:
        lines = f.readlines()

    # 过滤掉以PREPARED_开头的条目
    new_lines = []
    for line in lines:
        parts = line.strip().split('\t')
        if len(parts) >= 1 and not parts[0].startswith("PREPARED_"):
            new_lines.append(line)

    # 写回instruments文件
    with open(instruments_file, 'w') as f:
        f.writelines(new_lines)

    logger.info(f"instruments文件清理完成: {instruments_file}")

def verify_data(qlib_dir, symbol, freq="5min"):
    """
    验证qlib数据

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
        freq: 数据频率
    """
    logger.info(f"验证qlib数据: {qlib_dir} 股票代码: {symbol}")

    # 检查日历文件
    calendar_file = os.path.join(qlib_dir, "calendars", f"{freq}.txt")
    if not os.path.exists(calendar_file):
        logger.error(f"日历文件不存在: {calendar_file}")
        return False

    with open(calendar_file, 'r') as f:
        calendars = [line.strip() for line in f.readlines()]

    logger.info(f"日历数据总数: {len(calendars)}")
    if calendars:
        logger.info(f"日历数据范围: {calendars[0]} 至 {calendars[-1]}")
    else:
        logger.warning("日历数据为空")

    # 检查股票列表文件
    instruments_file = os.path.join(qlib_dir, "instruments", "all.txt")
    if not os.path.exists(instruments_file):
        logger.error(f"股票列表文件不存在: {instruments_file}")
        return False

    with open(instruments_file, 'r') as f:
        instruments = f.readlines()

    # 查找当前股票的条目
    symbol_entry = None
    for line in instruments:
        if line.startswith(symbol + "\t"):
            symbol_entry = line.strip()
            break

    if symbol_entry:
        logger.info(f"股票 {symbol} 条目: {symbol_entry}")
    else:
        logger.error(f"股票列表中找不到 {symbol}")
        return False

    # 检查特征文件
    features_dir = os.path.join(qlib_dir, "features", symbol)
    if not os.path.exists(features_dir):
        logger.error(f"特征目录不存在: {features_dir}")
        return False

    feature_files = [f for f in os.listdir(features_dir) if f.endswith(f"{freq}.bin")]
    logger.info(f"特征数量: {len(feature_files)}")
    logger.info(f"特征列表: {feature_files}")

    return True

def verify_qlib_data(qlib_dir, symbol, start_date=None, end_date=None):
    """
    验证qlib中的数据与原始数据是否一致

    Args:
        qlib_dir: qlib数据目录
        symbol: 股票代码
        start_date: 开始日期
        end_date: 结束日期
    """
    logger.info(f"验证qlib数据与原始数据是否一致: {symbol}")

    # 初始化qlib
    qlib.init(provider_uri=qlib_dir, region=REG_CN)

    # 如果没有提供日期范围，从原始数据中获取
    if start_date is None or end_date is None:
        original_file = f"kline_{symbol}.sh_origin.csv"
        if os.path.exists(original_file):
            original_df = pd.read_csv(original_file)
            original_df['datetime'] = pd.to_datetime(original_df['datetime'], format='%Y%m%d%H%M%S')
            if start_date is None:
                start_date = original_df['datetime'].min().strftime('%Y-%m-%d')
            if end_date is None:
                end_date = original_df['datetime'].max().strftime('%Y-%m-%d')

    # 从qlib中获取数据
    fields = ['$open', '$high', '$low', '$close', '$volume', '$amount']
    try:
        qlib_df = D.features([symbol], fields, start_time=start_date, end_time=end_date, freq='5min')

        # 重命名列以便比较
        qlib_df.columns = [c.replace('$', '') for c in qlib_df.columns]

        # 读取原始数据
        original_file = f"kline_{symbol}.sh_origin.csv"
        if not os.path.exists(original_file):
            logger.error(f"原始文件不存在: {original_file}")
            return

        original_df = pd.read_csv(original_file)

        # 确保日期格式一致
        original_df['datetime'] = pd.to_datetime(original_df['datetime'], format='%Y%m%d%H%M%S')

        # 重命名列以便比较
        column_mapping = {
            'datetime': 'date',
            'openPrice': 'open',
            'highPrice': 'high',
            'lowPrice': 'low',
            'closePrice': 'close',
            'tradeVolume': 'volume',
            'amount': 'amount'
        }
        original_df = original_df.rename(columns=column_mapping)

        # 打印数据统计信息
        logger.info(f"qlib数据行数: {len(qlib_df)}")
        logger.info(f"原始数据行数: {len(original_df)}")

        # 重置qlib数据的索引以便合并
        qlib_df = qlib_df.reset_index()
        qlib_df = qlib_df.rename(columns={'datetime': 'date'})

        # 合并数据进行比较
        merged_df = pd.merge(
            qlib_df,
            original_df[['date', 'open', 'high', 'low', 'close', 'volume', 'amount']],
            on='date',
            how='inner',
            suffixes=('_qlib', '_orig')
        )

        logger.info(f"合并后的数据行数: {len(merged_df)}")

        # 计算差异
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            merged_df[f'{field}_diff'] = merged_df[f'{field}_qlib'] - merged_df[f'{field}_orig']

        # 检查是否有差异
        has_diff = False
        for field in ['open', 'high', 'low', 'close', 'volume', 'amount']:
            diff_col = f'{field}_diff'
            if not np.allclose(merged_df[diff_col], 0, atol=1e-10):
                has_diff = True
                non_zero_diff = merged_df[merged_df[diff_col] != 0]
                logger.info(f"{diff_col} 存在差异的行数: {len(non_zero_diff)}")
                if len(non_zero_diff) > 0:
                    logger.info(f"差异统计: 最小值={non_zero_diff[diff_col].min()}, 最大值={non_zero_diff[diff_col].max()}, 平均值={non_zero_diff[diff_col].mean()}")
                    logger.info(f"差异示例:\n{non_zero_diff[[f'{field}_qlib', f'{field}_orig', diff_col]].head()}")

        if not has_diff:
            logger.info("所有数据完全匹配!")

        # 检查是否存在固定比例的差异
        if has_diff:
            # 计算每个时间点的比例
            ratios = merged_df['close_qlib'] / merged_df['close_orig']

            # 计算比例的统计信息
            ratio_mean = ratios.mean()
            ratio_std = ratios.std()

            logger.info(f"价格比例统计:")
            logger.info(f"平均比例: {ratio_mean}")
            logger.info(f"比例标准差: {ratio_std}")

            # 如果标准差很小，说明存在固定比例的差异
            if ratio_std < 0.01:
                logger.info(f"发现固定比例的差异，比例约为: {ratio_mean:.4f}")
                logger.info(f"这可能是由于单位转换或者其他系统性因素导致的")

        return merged_df

    except Exception as e:
        logger.error(f"从qlib获取数据时出错: {e}")
        return None

if __name__ == "__main__":
    qlib_dir = "./qlib_data"
    freq = "5min"

    # 定义两个股票代码的配置
    stock_configs = {
        "513980": {
            "input_file": "kline_513980.sh_origin.csv",
            "output_file": "prepared_513980.csv"
        },
        "513120": {
            "input_file": "kline_513120.sh_origin.csv",
            "output_file": "prepared_513120.csv"
        }
    }

    # 检查命令行参数
    if len(sys.argv) > 1 and sys.argv[1] in stock_configs:
        # 如果提供了有效的股票代码参数，只处理该股票
        symbols_to_process = [sys.argv[1]]
    else:
        # 否则处理所有配置的股票
        symbols_to_process = list(stock_configs.keys())

    for symbol in symbols_to_process:
        logger.info(f"开始处理股票: {symbol}")
        config = stock_configs[symbol]

        # 准备数据
        prepare_data(config["input_file"], config["output_file"], symbol)

        # 导入到qlib
        dump_to_qlib(config["output_file"], qlib_dir, symbol, freq)

        # 验证数据
        verify_data(qlib_dir, symbol, freq)

        # 验证qlib数据与原始数据是否一致
        verify_qlib_data(qlib_dir, symbol)

        logger.info(f"股票 {symbol} 数据处理完成")

    logger.info("所有数据处理完成")
